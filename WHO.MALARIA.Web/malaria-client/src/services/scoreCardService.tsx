﻿import i18next from "i18next";
import { Constants } from "../models/Constants";
import { StatusCode } from "../models/Enums";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import {
  FinalizeScoreCardModel,
  ScoreCardRequestModel,
  SubObjectiveIndicatorModel,
} from "../models/ScoreCard/ScoreCardModel";
import { getCall, postCall } from "./api";
import { notificationService } from "./notificationService";

export const scoreCardService = {
  /** Get Score Card Survey Details
   *  @param assessmentId A string value
   */
  getScoreCardSurvey: (assessmentId: string) =>
    new Promise<SubObjectiveIndicatorModel>((resolve, reject) => {
      getCall(
        Constants.Api.Url.GET_SCORE_CARD_SURVEY.replace("{0}", assessmentId)
      ).then((response: any) => {
        if (response) {
          resolve(response);
        } else {
          resolve(SubObjectiveIndicatorModel.init());
        }
      });
    }),

  /** Generate Score Card Template
   *  @param assessmentId A string value
   */
  generateScoreCardTemplate: (assessmentId: string) =>
    new Promise<boolean>((resolve, reject) => {
      const url = Constants.Api.Url.GENERATE_SCORE_CARD_EXCEL_FILE.replace(
        "{0}",
        assessmentId
      );
      getCall(url, "blob")
        .then((response: any) => {
          resolve(response);
        })
        .catch((error: ErrorModel) => {
          console.error("ScoreCard Export Error:", error);

          let errorMessage = i18next.t("Errors.SomethingWentWrong");

          // Provide more specific error messages based on status code
          if (error && error.statusCode) {
            switch (error.statusCode) {
              case StatusCode.PreConditionFailed:
                errorMessage =
                  "You don't have permission to export the scorecard. Please ensure the assessment is finalized and you have the required permissions.";
                break;
              case StatusCode.Forbidden:
                errorMessage =
                  "Access forbidden. Please check your permissions to export scorecards.";
                break;
              case StatusCode.NotFound:
                errorMessage =
                  "Scorecard data not found. Please ensure the scorecard has been saved and finalized.";
                break;
              case StatusCode.UnAuthorized:
                errorMessage =
                  "You are not authorized to export this scorecard. Please log in again or contact your administrator.";
                break;
              case StatusCode.InternalServerError:
                errorMessage =
                  "Server error occurred while generating the scorecard. Please try again or contact support.";
                break;
              default:
                errorMessage =
                  "Failed to export scorecard. Please try again or contact support if the problem persists.";
                break;
            }
          }

          if (error && error.statusCode !== StatusCode.PreConditionFailed) {
            notificationService.sendMessage(
              new BaseMessageModel(
                errorMessage,
                error.statusCode || StatusCode.InternalServerError
              )
            );
          }
          reject(true);
        });
    }),

  /** Save Score Card Survey */
  saveScoreCardSurvey: (request: ScoreCardRequestModel) =>
    new Promise<boolean>((resolve, reject) => {
      if (request.indicatorDetails.length > 0) {
        postCall<ScoreCardRequestModel>(
          Constants.Api.Url.SAVE_SCORE_CARD_SURVEY,
          request
        ).then(response => {
          resolve(response);
          if (response) {
            notificationService.sendMessage(
              new BaseMessageModel(
                i18next.t(
                  "Assessment.ReportGeneration.ScoreCard.Message.SaveScroreCardSuccessMessage"
                ),
                StatusCode.Created
              )
            );
          } else {
            notificationService.sendMessage(
              new BaseMessageModel(
                i18next.t(
                  "Assessment.ReportGeneration.ScoreCard.Message.SaveScoreCardErrorMessage"
                ),
                StatusCode.PreConditionFailed
              )
            );
          }
        });
      } else {
        notificationService.sendMessage(
          new BaseMessageModel(
            i18next.t(
              "Assessment.ReportGeneration.ScoreCard.Message.SaveScoreCardNothingChangedMessage"
            ),
            StatusCode.PreConditionFailed
          )
        );
      }
    }),

  /** Finalize Score Card Survey Response */
  finalizeScoreCardSurvey: (
    request: FinalizeScoreCardModel,
    isFinalize: boolean
  ) =>
    new Promise((resolve, reject) => {
      if (isFinalize) {
        postCall<FinalizeScoreCardModel>(
          Constants.Api.Url.FINALIZE_SCORE_CARD_SURVEY,
          request
        ).then(response => {
          if (response) {
            notificationService.sendMessage(
              new BaseMessageModel(
                i18next.t(
                  "Assessment.ReportGeneration.ScoreCard.Message.FinalizeScroreSuccessMessage"
                ),
                StatusCode.Created
              )
            );
          } else {
            notificationService.sendMessage(
              new BaseMessageModel(
                i18next.t(
                  "Assessment.ReportGeneration.ScoreCard.Message.FinalizeScoreCardErrorMessage"
                ),
                StatusCode.PreConditionFailed
              )
            );
          }
          resolve(response);
        });
      } else {
        notificationService.sendMessage(
          new BaseMessageModel(
            i18next.t(
              "Assessment.ReportGeneration.ScoreCard.Message.ScoreCardSaveFinalizeErrorMessage"
            ),
            StatusCode.UnAuthorized
          )
        );
      }
    }),

  /** Check whether score card can be generated */
  canScoreCardBeGenerated: (assessmentId: string) =>
    new Promise((resolve, reject) => {
      getCall(
        Constants.Api.Url.CAN_SCORE_CARD_BE_GENERATED.replace(
          "{0}",
          assessmentId
        )
      ).then((response: any) => {
        if (response) {
          resolve(response);
        }
      });
    }),

  /** Check whether score card data is saved or not */
  hasScoreCardData: (assessmentId: string) =>
    new Promise((resolve, reject) => {
      getCall(
        Constants.Api.Url.HAS_SCORE_CARD_DATA.replace("{0}", assessmentId)
      ).then((response: any) => {
        if (response) {
          resolve(response);
        }
      });
    }),
};
