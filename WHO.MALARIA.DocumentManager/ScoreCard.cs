﻿using ClosedXML.Excel;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.DocumentManager
{
    /// <summary>
    ///  This class contains methods related to modify excel and perform operation on the each sheet  
    /// </summary>
    public class ScoreCard : IScoreCard
    {
        private readonly ITranslationService _translationService;

        public ScoreCard(ITranslationService translationService)
        {
            _translationService = translationService;
        }

        /// <summary>
        /// Generate excel report workbook for score card
        /// </summary>
        /// <param name="indicatorsWithMetNotMetDetails">Datatable which contains met not met details for survey indicators</param>
        /// <param name="surveyPercentage">Survey score percentage</param>
        /// <returns>Excel workbook response in byte array</returns>
        public byte[] GenerateExcel(DataTable indicatorsWithMetNotMetDetails, string assessmentYear, bool IsRapidAssessment)
        {
            // Validate input parameters
            if (indicatorsWithMetNotMetDetails == null)
            {
                throw new ArgumentNullException(nameof(indicatorsWithMetNotMetDetails), "Indicators data table cannot be null");
            }

            if (string.IsNullOrEmpty(assessmentYear))
            {
                assessmentYear = "N/A";
            }

            using (MemoryStream memoryStream = new MemoryStream())
            {
                XLWorkbook workbook = new XLWorkbook();

                IXLWorksheet worksheet = workbook.Worksheets.Add(_translationService.GetTranslation(DataAnalysisExport.ScoreCardFileName));

                worksheet.Cell(1, 1).SetValue<string>(_translationService.GetTranslation(DataAnalysisExport.AssessmentYear));

                worksheet.Cell(1, 2).SetValue<string?>(assessmentYear);

                // Only insert table if we have data
                if (indicatorsWithMetNotMetDetails.Rows.Count > 0)
                {
                    worksheet.Cell(3, 1).InsertTable(indicatorsWithMetNotMetDetails);
                }
                else
                {
                    // Add headers even if no data
                    worksheet.Cell(3, 1).SetValue("No scorecard data available");
                }

                worksheet.Columns(1, 9).Width = 25;

                if (indicatorsWithMetNotMetDetails.Rows.Count > 0 && indicatorsWithMetNotMetDetails.Columns.Count >= 4)
                {
                    worksheet.Cell("A" + 3).Value = indicatorsWithMetNotMetDetails.Columns[0].ColumnName;
                    worksheet.Cell("B" + 3).Value = indicatorsWithMetNotMetDetails.Columns[1].ColumnName;
                    worksheet.Cell("C" + 3).SetDataType(XLDataType.Text);
                    worksheet.Cell("C" + 3).SetValue(indicatorsWithMetNotMetDetails.Columns[2].ColumnName);
                    worksheet.Cell("D" + 3).Value = indicatorsWithMetNotMetDetails.Columns[3].ColumnName;

                    if (IsRapidAssessment)
                    {
                        worksheet.Cell("E" + 3).Value = "Result";
                        worksheet.Column(6).Hide();
                        worksheet.Column(7).Hide();
                    }
                    else
                    {
                        // Check if we have enough columns before accessing them
                        if (indicatorsWithMetNotMetDetails.Columns.Count > 4)
                            worksheet.Cell("E" + 3).Value = indicatorsWithMetNotMetDetails.Columns[4].ColumnName;
                        if (indicatorsWithMetNotMetDetails.Columns.Count > 5)
                            worksheet.Cell("F" + 3).Value = indicatorsWithMetNotMetDetails.Columns[5].ColumnName;
                        if (indicatorsWithMetNotMetDetails.Columns.Count > 6)
                            worksheet.Cell("G" + 3).Value = indicatorsWithMetNotMetDetails.Columns[6].ColumnName;
                    }

                    string met = _translationService.GetTranslation(DataAnalysisExport.Met);
                    string notMet = _translationService.GetTranslation(DataAnalysisExport.NotMet);
                    string partiallyMet = _translationService.GetTranslation(DataAnalysisExport.PartiallyMet);
                    string notAssessed = _translationService.GetTranslation(DataAnalysisExport.NotAssessed);
                    string deskReviewAndDQAResult = _translationService.GetTranslation(DataAnalysisExport.DeskReviewAndDQAResult);
                    string surveyResult = _translationService.GetTranslation(DataAnalysisExport.SurveyResult);
                    string serviceDeliveryResult = _translationService.GetTranslation(DataAnalysisExport.ServiceDeliveryResult);

                    for (int i = 0; i < indicatorsWithMetNotMetDetails.Rows.Count; i++)
                    {
                        worksheet.Cell("A" + (i + 4)).Value = indicatorsWithMetNotMetDetails.Rows[i][0];
                        worksheet.Cell("B" + (i + 4)).Value = indicatorsWithMetNotMetDetails.Rows[i][1];
                        worksheet.Cell("C" + (i + 4)).SetDataType(XLDataType.Text);
                        worksheet.Cell("C" + (i + 4)).SetValue(indicatorsWithMetNotMetDetails.Rows[i][2]);
                        worksheet.Cell("D" + (i + 4)).Value = indicatorsWithMetNotMetDetails.Rows[i][3];

                        if (indicatorsWithMetNotMetDetails.Rows[i][deskReviewAndDQAResult].ToString() == ((int)MetNotMetStatus.Met).ToString() ||
                            indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.Met).ToString())
                        {
                            worksheet.Cell("E" + (i + 4)).Value = met;
                            worksheet.Cells("E" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#C6EFCE");
                            worksheet.Cells("E" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#006100");
                            worksheet.Cells("E" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }
                        else if (indicatorsWithMetNotMetDetails.Rows[i][deskReviewAndDQAResult].ToString() == ((int)MetNotMetStatus.NotMet).ToString() ||
                            indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.NotMet).ToString())
                        {
                            worksheet.Cell("E" + (i + 4)).Value = notMet;
                            worksheet.Cells("E" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFC7CE");
                            worksheet.Cells("E" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#9C0006");
                            worksheet.Cells("E" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }
                        else if (indicatorsWithMetNotMetDetails.Rows[i][deskReviewAndDQAResult].ToString() == ((int)MetNotMetStatus.PartiallyMet).ToString() ||
                            indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.PartiallyMet).ToString())
                        {
                            worksheet.Cell("E" + (i + 4)).Value = partiallyMet;
                            worksheet.Cells("E" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFEB9C");
                            worksheet.Cells("E" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#9C5700");
                            worksheet.Cells("E" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }
                        else if (indicatorsWithMetNotMetDetails.Rows[i][deskReviewAndDQAResult].ToString() == ((int)MetNotMetStatus.NotAssessed).ToString() ||
                            indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.NotAssessed).ToString())
                        {
                            worksheet.Cell("E" + (i + 4)).Value = notAssessed;
                            worksheet.Cells("E" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }
                        else
                        {
                            worksheet.Cell("E" + (i + 4)).Value = "";
                            worksheet.Cells("E" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }
                        worksheet.Cells("E" + (i + 4)).Style.Border.OutsideBorderColor = XLColor.FromHtml("#ececec");

                        if (!IsRapidAssessment)
                        {
                            if (indicatorsWithMetNotMetDetails.Rows[i][surveyResult].ToString() == ((int)MetNotMetStatus.Met).ToString())
                            {
                                worksheet.Cell("F" + (i + 4)).Value = met;
                                worksheet.Cells("F" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#C6EFCE");
                                worksheet.Cells("F" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#006100");
                                worksheet.Cells("F" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else if (indicatorsWithMetNotMetDetails.Rows[i][surveyResult].ToString() == ((int)MetNotMetStatus.NotMet).ToString())
                            {
                                worksheet.Cell("F" + (i + 4)).Value = notMet;
                                worksheet.Cells("F" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFC7CE");
                                worksheet.Cells("F" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#9C0006");
                                worksheet.Cells("F" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else if (indicatorsWithMetNotMetDetails.Rows[i][surveyResult].ToString() == ((int)MetNotMetStatus.PartiallyMet).ToString())
                            {
                                worksheet.Cell("F" + (i + 4)).Value = partiallyMet;
                                worksheet.Cells("F" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFEB9C");
                                worksheet.Cells("F" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#9C5700");
                                worksheet.Cells("F" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else if (indicatorsWithMetNotMetDetails.Rows[i][surveyResult].ToString() == ((int)MetNotMetStatus.NotAssessed).ToString())
                            {
                                worksheet.Cell("F" + (i + 4)).Value = notAssessed;
                                worksheet.Cells("F" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else
                            {
                                worksheet.Cell("F" + (i + 4)).Value = "";
                                worksheet.Cells("F" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            worksheet.Cells("F" + (i + 4)).Style.Border.OutsideBorderColor = XLColor.FromHtml("#ececec");
                        }
                        if (!IsRapidAssessment)
                        {
                            if (indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.Met).ToString())
                            {
                                worksheet.Cell("G" + (i + 4)).Value = met;
                                worksheet.Cells("G" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#C6EFCE");
                                worksheet.Cells("G" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#006100");
                                worksheet.Cells("G" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else if (indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.NotMet).ToString())
                            {
                                worksheet.Cell("G" + (i + 4)).Value = notMet;
                                worksheet.Cells("G" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFC7CE");
                                worksheet.Cells("G" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#9C0006");
                                worksheet.Cells("G" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else if (indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.PartiallyMet).ToString())
                            {
                                worksheet.Cell("G" + (i + 4)).Value = partiallyMet;
                                worksheet.Cells("G" + (i + 4)).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFEB9C");
                                worksheet.Cells("G" + (i + 4)).Style.Font.FontColor = XLColor.FromHtml("#9C5700");
                                worksheet.Cells("G" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else if (indicatorsWithMetNotMetDetails.Rows[i][serviceDeliveryResult].ToString() == ((int)MetNotMetStatus.NotAssessed).ToString())
                            {
                                worksheet.Cell("G" + (i + 4)).Value = notAssessed;
                                worksheet.Cells("G" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            else
                            {
                                worksheet.Cell("G" + (i + 4)).Value = "";
                                worksheet.Cells("G" + (i + 4)).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }
                            worksheet.Cells("G" + (i + 4)).Style.Border.OutsideBorderColor = XLColor.FromHtml("#ececec");
                        }
                    }
                }

                worksheet.Columns(5, 7).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                worksheet.Columns().AdjustToContents();

                workbook.SaveAs(memoryStream);
                return memoryStream.ToArray();
            }
        }
    }
}